"""
Test script to check if imports are working correctly.
"""

try:
    import colorama
    print("Colorama imported successfully!")
except ImportError as e:
    print(f"Failed to import colorama: {e}")

try:
    from langchain.chains import LLMChain
    print("LangChain imported successfully!")
except ImportError as e:
    print(f"Failed to import LangChain: {e}")

try:
    from langchain_groq import ChatGroq
    print("LangChain Groq imported successfully!")
except ImportError as e:
    print(f"Failed to import LangChain Groq: {e}")

try:
    import dotenv
    print("Python-dotenv imported successfully!")
except ImportError as e:
    print(f"Failed to import python-dotenv: {e}")

try:
    import tqdm
    print("tqdm imported successfully!")
except ImportError as e:
    print(f"Failed to import tqdm: {e}")
