"""
Working LinkedIn Conversation Tool
Simple tool to generate conversational messages based on past conversations.
"""

import os
import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_ai():
    """Setup the AI model."""
    try:
        from langchain.chains import LLMChain
        from langchain.prompts import PromptTemplate
        from langchain_groq import ChatGroq
        
        api_key = os.getenv("GROQ_API_KEY")
        if not api_key:
            print("❌ Please add your GROQ_API_KEY to the .env file")
            return None, None, None
            
        llm = ChatGroq(
            groq_api_key=api_key,
            model_name="llama3-70b-8192",
            temperature=0.7,
            max_tokens=500
        )
        return llm, LLMChain, PromptTemplate
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please install: pip install langchain langchain-groq")
        return None, None, None
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return None, None, None

def load_conversations():
    """Load existing conversations from file."""
    try:
        with open("conversations.json", 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def save_conversations(conversations):
    """Save conversations to file."""
    with open("conversations.json", 'w') as f:
        json.dump(conversations, f, indent=2)

def add_message_to_history(contact_name, message, sender="me"):
    """Add a message to conversation history."""
    conversations = load_conversations()
    
    if contact_name not in conversations:
        conversations[contact_name] = []
    
    conversations[contact_name].append({
        "timestamp": datetime.now().isoformat(),
        "sender": sender,
        "message": message
    })
    
    save_conversations(conversations)
    print(f"✅ Message added to {contact_name}'s conversation history")

def generate_response(contact_name, llm, LLMChain, PromptTemplate, context=""):
    """Generate a conversational response based on message history."""
    conversations = load_conversations()
    
    if contact_name not in conversations:
        print(f"❌ No conversation history found for {contact_name}")
        return None
    
    # Get recent conversation history
    recent_messages = conversations[contact_name][-5:]  # Last 5 messages
    
    # Format conversation history
    history_text = ""
    for msg in recent_messages:
        sender = "You" if msg["sender"] == "me" else contact_name
        history_text += f"{sender}: {msg['message']}\n"
    
    # Create prompt for generating response
    prompt_template = """
You are helping to write a friendly, professional LinkedIn message to continue a conversation with {contact_name}.

Recent conversation history:
{conversation_history}

Additional context: {context}

Generate a natural, conversational follow-up message that:
1. References something from the previous conversation
2. Adds value or asks a thoughtful question
3. Maintains a professional but friendly tone
4. Is concise (under 200 words)
5. Encourages continued conversation

Message:
"""
    
    prompt = PromptTemplate(
        input_variables=["contact_name", "conversation_history", "context"],
        template=prompt_template
    )
    
    chain = LLMChain(llm=llm, prompt=prompt)
    
    try:
        response = chain.run({
            "contact_name": contact_name,
            "conversation_history": history_text,
            "context": context
        })
        return response.strip()
    except Exception as e:
        return f"❌ Error generating response: {e}"

def show_conversation_history(contact_name):
    """Display conversation history for a contact."""
    conversations = load_conversations()
    
    if contact_name not in conversations:
        print(f"❌ No conversation history found for {contact_name}")
        return
    
    print(f"\n💬 Conversation History with {contact_name}")
    print("=" * 50)
    
    for msg in conversations[contact_name]:
        timestamp = datetime.fromisoformat(msg["timestamp"]).strftime("%Y-%m-%d %H:%M")
        sender = "You" if msg["sender"] == "me" else contact_name
        print(f"[{timestamp}] {sender}: {msg['message']}")
    
    print("=" * 50)

def list_contacts():
    """List all contacts with conversation history."""
    conversations = load_conversations()
    
    if not conversations:
        print("❌ No conversations found")
        return
    
    print("\n👥 Your LinkedIn Contacts with Conversation History:")
    print("=" * 50)
    
    for i, contact in enumerate(conversations.keys(), 1):
        msg_count = len(conversations[contact])
        last_msg = conversations[contact][-1]
        last_date = datetime.fromisoformat(last_msg["timestamp"]).strftime("%Y-%m-%d")
        print(f"{i}. {contact} ({msg_count} messages, last: {last_date})")
    
    print("=" * 50)

def main():
    """Main function."""
    print("🤖 Simple LinkedIn Conversation Tool")
    print("=" * 40)
    
    # Setup AI
    llm, LLMChain, PromptTemplate = setup_ai()
    
    if not llm:
        print("❌ AI setup failed. You can still manage conversation history manually.")
        ai_available = False
    else:
        print("✅ AI ready for generating responses!")
        ai_available = True
    
    while True:
        print("\n📋 What would you like to do?")
        if ai_available:
            print("1. Generate AI response to a contact")
        print("2. Add message to conversation history")
        print("3. View conversation history")
        print("4. List all contacts")
        print("5. Exit")
        
        choice = input("\nEnter your choice: ").strip()
        
        if choice == "1" and ai_available:
            list_contacts()
            contact_name = input("\nEnter contact name: ").strip()
            if contact_name:
                context = input("Any additional context (optional): ").strip()
                print("\n🤖 Generating response...")
                response = generate_response(contact_name, llm, LLMChain, PromptTemplate, context)
                if response:
                    print(f"\n💬 Suggested message for {contact_name}:")
                    print("=" * 50)
                    print(response)
                    print("=" * 50)
                    
                    # Ask if user wants to save this as sent
                    save = input("\nDid you send this message? (y/n): ").strip().lower()
                    if save == 'y':
                        add_message_to_history(contact_name, response, "me")
        
        elif choice == "2":
            contact_name = input("Enter contact name: ").strip()
            message = input("Enter the message: ").strip()
            sender = input("Who sent this? (me/them): ").strip().lower()
            
            if sender not in ["me", "them"]:
                sender = "them"
            
            if contact_name and message:
                add_message_to_history(contact_name, message, sender)
        
        elif choice == "3":
            list_contacts()
            contact_name = input("\nEnter contact name: ").strip()
            if contact_name:
                show_conversation_history(contact_name)
        
        elif choice == "4":
            list_contacts()
        
        elif choice == "5":
            print("👋 Thanks for using LinkedIn Conversation Tool!")
            break
        
        else:
            if choice == "1" and not ai_available:
                print("❌ AI features not available. Please check your setup.")
            else:
                print("❌ Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
