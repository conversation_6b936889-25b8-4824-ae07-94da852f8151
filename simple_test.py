"""
Simplified version of the LinkedIn outreach tool for testing.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def main():
    """
    Main function to run the simplified LinkedIn outreach tool.
    """
    print("\n=== LinkedIn Outreach Tool with AI (Simple Test) ===\n")
    
    # Check if GROQ_API_KEY is set
    api_key = os.getenv("GROQ_API_KEY")
    if api_key:
        print("GROQ_API_KEY found in environment variables.")
    else:
        print("GROQ_API_KEY not found in environment variables.")
        print("Please add your Groq API key to the .env file.")
    
    # Get user information
    print("\nPlease provide your information:")
    name = input("Your name: ")
    position = input("Your position: ")
    company = input("Your company: ")
    
    print(f"\nThank you, {name}!")
    print(f"Position: {position}")
    print(f"Company: {company}")
    
    print("\nThis is a simplified test of the LinkedIn outreach tool.")
    print("The full version would generate personalized messages using the Groq API.")

if __name__ == "__main__":
    main()
