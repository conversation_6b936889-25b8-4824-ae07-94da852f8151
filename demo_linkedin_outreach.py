"""
Demo script showing the LinkedIn outreach tool capabilities.
"""

import os
import time
from dotenv import load_dotenv
from langchain.chains import LLMChain
from langchain.prompts import PromptTemplate
from langchain_groq import ChatGroq

# Load environment variables
load_dotenv()

def setup_ai_model():
    """Set up the AI model."""
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        print("❌ GROQ_API_KEY not found. Please add it to your .env file.")
        return None
    
    return ChatGroq(
        groq_api_key=api_key,
        model_name="llama3-70b-8192",
        temperature=0.7,
        max_tokens=1024
    )

def generate_connection_request(llm, recipient_data, sender_data):
    """Generate a connection request message."""
    template = """
Create a personalized LinkedIn connection request message for {recipient_name} who works as {recipient_position} at {recipient_company}.

Additional context about the recipient:
{recipient_info}

My information:
Name: {sender_name}
Position: {sender_position}
Company: {sender_company}
Goal of connection: {connection_goal}

The message should be under 300 characters, professional, and personalized.
"""
    
    prompt = PromptTemplate(
        input_variables=["recipient_name", "recipient_position", "recipient_company", 
                        "recipient_info", "sender_name", "sender_position", 
                        "sender_company", "connection_goal"],
        template=template
    )
    
    chain = LLMChain(llm=llm, prompt=prompt)
    response = chain.run(recipient_data | sender_data)
    return response.strip()

def generate_follow_up(llm, recipient_data, sender_data, initial_message):
    """Generate a follow-up message."""
    template = """
Create a follow-up message for {recipient_name} who accepted my LinkedIn connection request 3 days ago.

Our initial connection message was: "{initial_message}"

Additional context about the recipient:
{recipient_info}

My information:
Name: {sender_name}
Position: {sender_position}
Company: {sender_company}
Goal of follow-up: {follow_up_goal}

The message should be professional, personalized, and provide clear value or a specific call to action.
"""
    
    prompt = PromptTemplate(
        input_variables=["recipient_name", "recipient_info", "initial_message",
                        "sender_name", "sender_position", "sender_company", "follow_up_goal"],
        template=template
    )
    
    chain = LLMChain(llm=llm, prompt=prompt)
    response = chain.run(recipient_data | sender_data | {"initial_message": initial_message})
    return response.strip()

def print_message(title, message, char_limit=None):
    """Print a formatted message."""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print('='*60)
    print(message)
    print('='*60)
    if char_limit:
        char_count = len(message)
        status = "✅" if char_count <= char_limit else "⚠️"
        print(f"{status} Character count: {char_count}/{char_limit}")
    print()

def main():
    """Run the demo."""
    print("🚀 LinkedIn Outreach Tool - AI Demo")
    print("="*50)
    
    # Setup AI model
    print("Setting up AI model...")
    llm = setup_ai_model()
    if not llm:
        return
    print("✅ AI model ready!")
    
    # Demo data
    sender_data = {
        "sender_name": "Alex Johnson",
        "sender_position": "Senior Data Scientist",
        "sender_company": "TechFlow AI"
    }
    
    # Scenario 1: Tech Professional
    print("\n📋 SCENARIO 1: Connecting with a Tech Professional")
    recipient_1 = {
        "recipient_name": "Sarah Chen",
        "recipient_position": "Machine Learning Engineer",
        "recipient_company": "Google",
        "recipient_info": "Specializes in computer vision and has published papers on deep learning. Active in AI community.",
        "connection_goal": "Discuss collaboration opportunities in AI research"
    }
    
    print("Generating connection request...")
    time.sleep(1)
    message_1 = generate_connection_request(llm, recipient_1, sender_data)
    print_message("Connection Request - Tech Professional", message_1, 300)
    
    # Generate follow-up for scenario 1
    follow_up_data_1 = recipient_1.copy()
    follow_up_data_1["follow_up_goal"] = "Share a relevant AI research paper and propose a virtual coffee chat"
    
    print("Generating follow-up message...")
    time.sleep(1)
    follow_up_1 = generate_follow_up(llm, follow_up_data_1, sender_data, message_1)
    print_message("Follow-up Message - Tech Professional", follow_up_1)
    
    # Scenario 2: Business Professional
    print("\n📋 SCENARIO 2: Connecting with a Business Professional")
    recipient_2 = {
        "recipient_name": "Michael Rodriguez",
        "recipient_position": "VP of Sales",
        "recipient_company": "SalesForce",
        "recipient_info": "15+ years in B2B sales, expert in CRM solutions and sales automation.",
        "connection_goal": "Explore potential partnership opportunities"
    }
    
    print("Generating connection request...")
    time.sleep(1)
    message_2 = generate_connection_request(llm, recipient_2, sender_data)
    print_message("Connection Request - Business Professional", message_2, 300)
    
    # Scenario 3: Startup Founder
    print("\n📋 SCENARIO 3: Connecting with a Startup Founder")
    recipient_3 = {
        "recipient_name": "Emma Thompson",
        "recipient_position": "Founder & CEO",
        "recipient_company": "HealthTech Innovations",
        "recipient_info": "Building AI-powered healthcare solutions. Recently raised Series A funding.",
        "connection_goal": "Discuss potential collaboration in healthcare AI"
    }
    
    print("Generating connection request...")
    time.sleep(1)
    message_3 = generate_connection_request(llm, recipient_3, sender_data)
    print_message("Connection Request - Startup Founder", message_3, 300)
    
    print("🎉 Demo completed successfully!")
    print("\n💡 Key Features Demonstrated:")
    print("✅ Personalized connection requests based on recipient's background")
    print("✅ Professional tone and appropriate length for LinkedIn")
    print("✅ Context-aware messaging that mentions specific details")
    print("✅ Follow-up message generation with clear value proposition")
    print("✅ Different messaging styles for different professional contexts")
    
    print("\n🔧 To use this tool:")
    print("1. Add your Groq API key to the .env file")
    print("2. Run 'python linkedin_outreach.py' for the interactive version")
    print("3. Customize prompts in prompts.py for your specific needs")

if __name__ == "__main__":
    main()
