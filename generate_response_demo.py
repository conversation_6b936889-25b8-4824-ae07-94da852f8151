"""
Demo: Generate AI response for <PERSON> based on conversation history
"""

import os
import json
from dotenv import load_dotenv
from langchain.chains import LL<PERSON>hain
from langchain.prompts import PromptTemplate
from langchain_groq import ChatGroq

load_dotenv()

def generate_response_for_john():
    """Generate a response for <PERSON> based on the conversation history."""
    
    # Setup AI
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        print("❌ Please add your GROQ_API_KEY to the .env file")
        return
    
    llm = ChatGroq(
        groq_api_key=api_key,
        model_name="llama3-70b-8192",
        temperature=0.7,
        max_tokens=500
    )
    
    # <PERSON>'s conversation history
    conversation_history = """
<PERSON>: Hey! Thanks for connecting. I saw your post about AI in healthcare - really interesting insights!

You: Thanks <PERSON>! I'm passionate about AI applications in healthcare. I noticed you work at MedTech Solutions - are you guys exploring AI integration?

<PERSON>: Yes, we're actually looking into AI for diagnostic imaging. It's still early stages but very promising. Would love to hear more about your experience in this space.
"""
    
    # Create prompt
    prompt_template = """
You are helping to write a friendly, professional LinkedIn message to continue a conversation with <PERSON>.

Recent conversation history:
{conversation_history}

John works at MedTech Solutions and they're exploring AI for diagnostic imaging. He's interested in learning about your experience in healthcare AI.

Generate a natural, conversational follow-up message that:
1. References his interest in diagnostic imaging AI
2. Shares relevant experience or insights
3. Offers specific value (like a resource, connection, or meeting)
4. Maintains a professional but friendly tone
5. Is concise (under 200 words)
6. Encourages continued conversation

Message:
"""
    
    prompt = PromptTemplate(
        input_variables=["conversation_history"],
        template=prompt_template
    )
    
    chain = LLMChain(llm=llm, prompt=prompt)
    
    try:
        response = chain.run({
            "conversation_history": conversation_history
        })
        
        print("🤖 AI-Generated Response for John Smith:")
        print("=" * 60)
        print(response.strip())
        print("=" * 60)
        print(f"Character count: {len(response.strip())}")
        
        return response.strip()
        
    except Exception as e:
        print(f"❌ Error generating response: {e}")
        return None

def generate_response_for_sarah():
    """Generate a response for Sarah Chen based on the conversation history."""
    
    # Setup AI
    api_key = os.getenv("GROQ_API_KEY")
    llm = ChatGroq(
        groq_api_key=api_key,
        model_name="llama3-70b-8192",
        temperature=0.7,
        max_tokens=500
    )
    
    # Sarah Chen's conversation history
    conversation_history = """
You: Hi Sarah! Great meeting you at the tech conference last week. Your presentation on machine learning was fantastic!

Sarah Chen: Thank you! I really enjoyed our conversation about data science applications. Your work on predictive analytics sounds fascinating.

You: I'd love to continue our discussion. Are you free for a virtual coffee chat sometime this week?

Sarah Chen: Absolutely! I'm free Thursday afternoon or Friday morning. What works better for you?
"""
    
    # Create prompt
    prompt_template = """
You are helping to write a friendly, professional LinkedIn message to continue a conversation with Sarah Chen.

Recent conversation history:
{conversation_history}

Sarah has agreed to a virtual coffee chat and offered Thursday afternoon or Friday morning. You need to respond to schedule the meeting.

Generate a natural, conversational follow-up message that:
1. Responds to her scheduling options
2. Suggests a specific time and platform (Zoom, Teams, etc.)
3. Mentions what you'd like to discuss
4. Maintains a professional but friendly tone
5. Is concise and clear

Message:
"""
    
    prompt = PromptTemplate(
        input_variables=["conversation_history"],
        template=prompt_template
    )
    
    chain = LLMChain(llm=llm, prompt=prompt)
    
    try:
        response = chain.run({
            "conversation_history": conversation_history
        })
        
        print("\n🤖 AI-Generated Response for Sarah Chen:")
        print("=" * 60)
        print(response.strip())
        print("=" * 60)
        print(f"Character count: {len(response.strip())}")
        
        return response.strip()
        
    except Exception as e:
        print(f"❌ Error generating response: {e}")
        return None

def main():
    """Run the demo."""
    print("🚀 LinkedIn Conversation AI Demo")
    print("Generating responses based on conversation history...")
    print()
    
    # Generate response for John Smith
    john_response = generate_response_for_john()
    
    # Generate response for Sarah Chen
    sarah_response = generate_response_for_sarah()
    
    print("\n✅ Demo completed!")
    print("\n💡 This shows how the AI:")
    print("• Analyzes conversation context")
    print("• References specific details from previous messages")
    print("• Generates contextually appropriate responses")
    print("• Maintains professional tone")
    print("• Provides clear next steps")

if __name__ == "__main__":
    main()
